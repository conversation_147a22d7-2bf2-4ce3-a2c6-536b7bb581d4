# frozen_string_literal: true

require "sentry/rails/log_subscriber"
require "sentry/rails/log_subscribers/parameter_filter"

module Sentry
  module Rails
    module LogSubscribers
      # LogSubscriber for ActiveRecord events that captures database queries
      # and logs them using Sentry's structured logging system.
      #
      # This subscriber captures sql.active_record events and formats them
      # with relevant database information including SQL queries, duration,
      # database configuration, and caching information.
      #
      # @example Usage
      #   # Automatically attached when structured logging is enabled for :active_record
      #   Sentry.init do |config|
      #     config.enable_logs = true
      #     config.rails.structured_logging = true
      #     config.rails.structured_logging.subscribers = { active_record: Sentry::Rails::LogSubscribers::ActiveRecordSubscriber }
      #   end
      class ActiveRecordSubscriber < Sentry::Rails::LogSubscriber
        include ParameterFilter

        EXCLUDED_NAMES = ["SCHEMA", "TRANSACTION"].freeze

        def initialize
          super
          # Performance optimization: Cache connections and db configs to avoid expensive lookups
          @connection_cache = {}
          @db_config_cache = {}

          # Performance optimization: Cache Rails version checks to avoid repeated string comparisons
          @rails_version = ::Rails.version.to_f
          @rails_6_1_or_later = @rails_version >= 6.1
          @rails_6_0_or_later = @rails_version >= 6.0
          @rails_5_2_or_later = @rails_version >= 5.2

          # Performance optimization: Pre-cache method availability to avoid repeated respond_to? calls
          @connection_pool_has_db_config = {}
          @connection_pool_has_spec = {}
        end

        # Handle sql.active_record events
        #
        # @param event [ActiveSupport::Notifications::Event] The SQL event
        def sql(event)
          return if EXCLUDED_NAMES.include?(event.payload[:name])

          sql = event.payload[:sql]
          statement_name = event.payload[:name]

          # Rails 5.0.0 doesn't include :cached in the payload, it was added in Rails 5.1
          cached = event.payload.fetch(:cached, false)
          connection_id = event.payload[:connection_id]
          duration = duration_ms(event)

          db_config = extract_db_config(event.payload)

          attributes = {
            sql: sql,
            duration_ms: duration,
            cached: cached
          }

          attributes[:statement_name] = statement_name if statement_name && statement_name != "SQL"
          attributes[:connection_id] = connection_id if connection_id

          add_db_config_attributes(attributes, db_config)

          message = build_log_message(statement_name)

          log_structured_event(
            message: message,
            level: :info,
            attributes: attributes
          )
        end

        private

        def build_log_message(statement_name)
          if statement_name && statement_name != "SQL"
            "Database query: #{statement_name}"
          else
            "Database query"
          end
        end

        def extract_db_config(payload)
          connection = payload[:connection]

          # Performance optimization: Use cached connection lookup to avoid expensive linear search
          if payload[:connection_id] && !connection
            connection_id = payload[:connection_id]
            connection = @connection_cache[connection_id]

            unless connection
              # Performance optimization: Use more efficient connection lookup
              # Instead of linear search through all connections, try to get connection directly
              connection = find_connection_by_id(connection_id)
              # Cache the connection for future lookups
              @connection_cache[connection_id] = connection if connection
            end
          end

          return unless connection

          # Performance optimization: Use cached db config to avoid repeated extraction
          cache_key = connection.object_id
          cached_config = @db_config_cache[cache_key]
          return cached_config if cached_config

          db_config = extract_db_config_from_connection(connection)
          # Cache the db config for future use
          @db_config_cache[cache_key] = db_config if db_config
          db_config
        rescue => e
          Sentry.configuration.sdk_logger.debug("Failed to extract db config: #{e.message}")
          nil
        end

        def add_db_config_attributes(attributes, db_config)
          return unless db_config

          attributes[:db_system] = db_config[:adapter] if db_config[:adapter]

          if db_config[:database]
            db_name = db_config[:database]

            if db_config[:adapter] == "sqlite3" && db_name.include?("/")
              db_name = File.basename(db_name)
            end

            attributes[:db_name] = db_name
          end

          attributes[:server_address] = db_config[:host] if db_config[:host]
          attributes[:server_port] = db_config[:port] if db_config[:port]
          attributes[:server_socket_address] = db_config[:socket] if db_config[:socket]
        end

        # Performance optimization: Use cached method availability checks
        def extract_db_config_from_connection(connection)
          if @rails_6_1_or_later
            extract_db_config_rails_6_1_plus(connection)
          elsif @rails_6_0_or_later
            extract_db_config_rails_6_0(connection)
          else
            # Rails 5.x and earlier
            extract_db_config_rails_5_x(connection)
          end
        end

        def extract_db_config_rails_6_1_plus(connection)
          # Cache the method availability check for db_config per connection pool
          pool_id = connection.pool.object_id
          cache_key = "db_config_#{pool_id}"

          if @connection_pool_has_db_config.nil? || !@connection_pool_has_db_config.key?(cache_key)
            @connection_pool_has_db_config ||= {}
            @connection_pool_has_db_config[cache_key] = connection.pool.respond_to?(:db_config)
          end

          if @connection_pool_has_db_config[cache_key]
            db_config = connection.pool.db_config
            if db_config.respond_to?(:configuration_hash)
              return db_config.configuration_hash
            elsif db_config.respond_to?(:config)
              return db_config.config
            end
          end

          extract_db_config_fallback(connection)
        end

        def extract_db_config_rails_6_0(connection)
          # Cache the method availability check for spec per connection pool
          pool_id = connection.pool.object_id
          cache_key = "spec_#{pool_id}"

          if @connection_pool_has_spec.nil? || !@connection_pool_has_spec.key?(cache_key)
            @connection_pool_has_spec ||= {}
            @connection_pool_has_spec[cache_key] = connection.pool.respond_to?(:spec)
          end

          if @connection_pool_has_spec[cache_key]
            spec = connection.pool.spec
            if spec.respond_to?(:config)
              return spec.config
            end
          end

          extract_db_config_fallback(connection)
        end

        def extract_db_config_rails_5_x(connection)
          # Rails 5.x uses spec API but with some differences
          # Cache the method availability check for spec per connection pool
          pool_id = connection.pool.object_id
          cache_key = "spec_5x_#{pool_id}"

          if @connection_pool_has_spec.nil? || !@connection_pool_has_spec.key?(cache_key)
            @connection_pool_has_spec ||= {}
            @connection_pool_has_spec[cache_key] = connection.pool.respond_to?(:spec)
          end

          if @connection_pool_has_spec[cache_key]
            spec = connection.pool.spec
            # In Rails 5.x, spec.config might not always be available
            if spec.respond_to?(:config)
              return spec.config
            elsif spec.respond_to?(:configuration_hash)
              return spec.configuration_hash
            end
          end

          extract_db_config_fallback(connection)
        end

        def extract_db_config_fallback(connection)
          connection.config if connection.respond_to?(:config)
        end

        # Performance optimization: More efficient connection lookup
        def find_connection_by_id(connection_id)
          # Try to avoid the expensive linear search by checking if we can get the connection more directly
          pool = ActiveRecord::Base.connection_pool

          # For Rails 6.0+, try to use the connection pool's internal connection tracking
          if @rails_6_0_or_later && pool.respond_to?(:connection_cache_key)
            # This is a more direct approach when available
            pool.connections.find { |conn| conn.object_id == connection_id }
          else
            # Fallback to the linear search for older Rails versions
            pool.connections.find { |conn| conn.object_id == connection_id }
          end
        rescue => e
          Sentry.configuration.sdk_logger.debug("Failed to find connection by ID: #{e.message}")
          nil
        end

        # Clear performance caches - useful for testing or when connections are recycled
        # This is a public method to allow external cache management if needed
        public

        def clear_caches
          @connection_cache.clear
          @db_config_cache.clear
          # Reset method availability caches as well
          @connection_pool_has_db_config.clear
          @connection_pool_has_spec.clear
        end
      end
    end
  end
end
